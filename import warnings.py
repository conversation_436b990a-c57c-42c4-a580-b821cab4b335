import warnings
import joblib

from sklearn.model_selection import StratifiedKFold
import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
from imblearn.over_sampling import SMOTE
from IPython.display import display, HTML
from sklearn.ensemble import (
    AdaBoostClassifier,
    ExtraTreesClassifier,
    GradientBoostingClassifier,
    RandomForestClassifier,
)
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.model_selection import GridSearchCV, KFold, train_test_split
from sklearn.naive_bayes import GaussianNB
from sklearn.neighbors import KNeighborsClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.svm import SVC
from sklearn.tree import DecisionTreeClassifier
warnings.filterwarnings("ignore")

# Bước 1: Tải dataset
# Thử tìm file CSV trong thư mục hiện tại hoặc yêu cầu người dùng cung cấp
import os
csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]
if csv_files:
    csv_file = csv_files[0]  # Lấy file CSV đầu tiên
    print(f"Đã tìm thấy file CSV: {csv_file}")
    df = pd.read_csv(csv_file)
else:
    print("Không tìm thấy file CSV. Vui lòng đặt file 'WA_Fn-UseC_-HR-Employee-Attrition.csv' vào thư mục hiện tại.")
    # Tạo dữ liệu mẫu để demo
    print("Tạo dữ liệu mẫu để demo...")
    import numpy as np
    np.random.seed(42)
    n_samples = 1000
    df = pd.DataFrame({
        'Age': np.random.randint(18, 65, n_samples),
        'Attrition': np.random.choice(['Yes', 'No'], n_samples, p=[0.2, 0.8]),
        'BusinessTravel': np.random.choice(['Travel_Rarely', 'Travel_Frequently', 'Non-Travel'], n_samples),
        'DailyRate': np.random.randint(100, 2000, n_samples),
        'Department': np.random.choice(['Sales', 'Research & Development', 'Human Resources'], n_samples),
        'DistanceFromHome': np.random.randint(1, 30, n_samples),
        'Education': np.random.randint(1, 5, n_samples),
        'EducationField': np.random.choice(['Life Sciences', 'Medical', 'Marketing', 'Technical Degree', 'Other'], n_samples),
        'EmployeeCount': [1] * n_samples,
        'EmployeeNumber': range(1, n_samples + 1),
        'EnvironmentSatisfaction': np.random.randint(1, 5, n_samples),
        'Gender': np.random.choice(['Male', 'Female'], n_samples),
        'HourlyRate': np.random.randint(30, 100, n_samples),
        'JobInvolvement': np.random.randint(1, 5, n_samples),
        'JobLevel': np.random.randint(1, 6, n_samples),
        'JobRole': np.random.choice(['Sales Executive', 'Research Scientist', 'Laboratory Technician', 'Manufacturing Director', 'Healthcare Representative'], n_samples),
        'JobSatisfaction': np.random.randint(1, 5, n_samples),
        'MaritalStatus': np.random.choice(['Single', 'Married', 'Divorced'], n_samples),
        'MonthlyIncome': np.random.randint(1000, 20000, n_samples),
        'MonthlyRate': np.random.randint(2000, 30000, n_samples),
        'NumCompaniesWorked': np.random.randint(0, 10, n_samples),
        'Over18': ['Y'] * n_samples,
        'OverTime': np.random.choice(['Yes', 'No'], n_samples),
        'PercentSalaryHike': np.random.randint(10, 25, n_samples),
        'PerformanceRating': np.random.randint(3, 5, n_samples),
        'RelationshipSatisfaction': np.random.randint(1, 5, n_samples),
        'StandardHours': [80] * n_samples,
        'StockOptionLevel': np.random.randint(0, 4, n_samples),
        'TotalWorkingYears': np.random.randint(0, 40, n_samples),
        'TrainingTimesLastYear': np.random.randint(0, 7, n_samples),
        'WorkLifeBalance': np.random.randint(1, 5, n_samples),
        'YearsAtCompany': np.random.randint(0, 40, n_samples),
        'YearsInCurrentRole': np.random.randint(0, 20, n_samples),
        'YearsSinceLastPromotion': np.random.randint(0, 15, n_samples),
        'YearsWithCurrManager': np.random.randint(0, 20, n_samples)
    })

# In bảng dữ liệu gốc với tên cột tiếng Anh
print(" Dữ liệu gốc (cột tiếng Anh):")
display(HTML(df.head().to_html(index=False)))

# Đổi tên cột sang tiếng Việt
df.rename(columns={
    'Age': 'Tuổi',
    'Attrition': 'Nghỉ_Việc',
    'BusinessTravel': 'Đi_Công_Tác',
    'DailyRate': 'Lương_Ngày',
    'Department': 'Phòng_Ban',
    'DistanceFromHome': 'Khoảng_Cách_Tới_Nhà',
    'Education': 'Trình_Độ_Học_Vấn',
    'EducationField': 'Ngành_Học',
    'EmployeeCount': 'Tổng_Nhân_Viên',
    'EmployeeNumber': 'Mã_Nhân_Viên',
    'EnvironmentSatisfaction': 'Mức_Độ_Hài_Lòng_Môi_Trường',
    'Gender': 'Giới_Tính',
    'HourlyRate': 'Lương_Giờ',
    'JobInvolvement': 'Mức_Độ_Tham_Gia_Công_Việc',
    'JobLevel': 'Cấp_Bậc_Công_Việc',
    'JobRole': 'Chức_Vụ',
    'JobSatisfaction': 'Mức_Độ_Hài_Lòng_Công_Việc',
    'MaritalStatus': 'Tình_Trạng_Hôn_Nhân',
    'MonthlyIncome': 'Lương_Tháng',
    'MonthlyRate': 'Lương_Tháng_Theo_Tỷ_Lệ',
    'NumCompaniesWorked': 'Số_Công_Ty_Đã_Làm',
    'Over18': 'Trên_18_Tuổi',
    'OverTime': 'Làm_Thêm',
    'PercentSalaryHike': 'Phần_Trăm_Tăng_Lương',
    'PerformanceRating': 'Đánh_Giá_Hiệu_Suất',
    'RelationshipSatisfaction': 'Mức_Hài_Lòng_Mối_Quan_Hệ',
    'StandardHours': 'Giờ_Làm_Tiêu_Chuẩn',
    'StockOptionLevel': 'Cấp_Độ_Tùy_Chọn_Cổ_Phần',
    'TotalWorkingYears': 'Tổng_Số_Năm_Làm_Việc',
    'TrainingTimesLastYear': 'Số_Lần_Đào_Tạo_Năm_Trước',
    'WorkLifeBalance': 'Cân_Bằng_Cuộc_Sống_Công_Việc',
    'YearsAtCompany': 'Số_Năm_Làm_Tại_Công_Ty',
    'YearsInCurrentRole': 'Số_Năm_Làm_Vị_Trí_Hiện_Tại',
    'YearsSinceLastPromotion': 'Số_Năm_Từ_Lần_Thăng_Chức_Gần_Nhất',
    'YearsWithCurrManager': 'Số_Năm_Với_Quản_Lý_Hiện_Tại'
}, inplace=True)

#  In bảng dữ liệu sau khi đổi tên sang tiếng Việt
print(" Dữ liệu sau khi đổi tên cột sang tiếng Việt:")
display(HTML(df.head().to_html(index=False)))

first_row_to_test = df.iloc[0].copy() #Lấy dòng đầu của bộ dữ liệu để kiểm thử sau

df.drop(['Mã_Nhân_Viên', 'Trên_18_Tuổi', 'Giờ_Làm_Tiêu_Chuẩn', 'Tổng_Nhân_Viên'], axis=1, inplace=True)

# Mã hóa biến mục tiêu 'Nghỉ_Việc' (0: Không, 1: Có)
le_y = LabelEncoder()
df['Nghỉ_Việc'] = le_y.fit_transform(df['Nghỉ_Việc'])

# Sử dụng One-Hot Encoding cho các biến phân loại
print(" Áp dụng One-Hot Encoding...")
categorical_cols = df.select_dtypes(include=['object']).columns
df = pd.get_dummies(df, columns=categorical_cols, drop_first=True)
print(f"Kích thước dữ liệu sau One-Hot Encoding: {df.shape}")

# Tách features và target
X = df.drop('Nghỉ_Việc', axis=1)
y = df['Nghỉ_Việc']

print(f"\nKích thước features: {X.shape}")
print(f"Phân phối target: {y.value_counts()}")

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42, stratify=y)
print(f"\nKích thước tập huấn luyện: {X_train.shape}")
print(f"Kích thước tập kiểm tra: {X_test.shape}")

# >> Lưu lại các cột sau khi xử lý để dùng cho dự đoán sau này
processed_columns = X.columns.tolist()

print("\nPhân phối lớp trước SMOTE (trên tập train):")
print(y_train.value_counts())

sm = SMOTE(random_state=42)
X_train_res, y_train_res = sm.fit_resample(X_train, y_train)

print("\nPhân phối lớp sau SMOTE (trên tập train):")
print(pd.Series(y_train_res).value_counts())

scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train_res)
X_test_scaled = scaler.transform(X_test) # Dùng scaler đã fit từ train

# Chuyển X_train_scaled về DataFrame để dễ thao tác và trực quan hơn
X_train_scaled_df = pd.DataFrame(X_train_scaled, columns=processed_columns)

# Thống kê tương quan giữa các biến với biến mục tiêu y_train_res
df_corr = X_train_scaled_df.copy()
df_corr['Attrition'] = y_train_res

# Tính hệ số tương quan Pearson với biến mục tiêu
correlations = df_corr.corr()['Attrition'].sort_values(ascending=False)
print("\nCác biến có tương quan với biến mục tiêu (Attrition) trên tập huấn luyện:")
display(correlations.head(15)) # In 15 biến hàng đầu

top_features = correlations.abs().sort_values(ascending=False).index[1:11]
plt.figure(figsize=(10,6))
sns.barplot(x=correlations[top_features], y=top_features, palette='viridis')
plt.title("Top 10 biến có tương quan mạnh nhất với 'Nghỉ_Việc' (trên tập huấn luyện)")
plt.xlabel("Hệ số tương quan Pearson")
plt.ylabel("Tên biến")
plt.show()

kf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42) # Chia dữ liệu với KFold = 5

models = {
    "KNN": (KNeighborsClassifier(), {'n_neighbors': [3, 5, 7]}),
    "DecisionTree": (DecisionTreeClassifier(), {'max_depth': [3, 5, 10]}),
    "RandomForest": (RandomForestClassifier(), {'n_estimators': [50, 100], 'max_depth': [5, 10]}),
    "SVM": (SVC(probability=True), {'C': [0.1, 1, 10], 'kernel': ['linear', 'rbf']}),
    "ANN": (MLPClassifier(max_iter=500), {'hidden_layer_sizes': [(50,), (100,)], 'activation': ['relu', 'tanh']}),

    "LogisticRegression": (
        LogisticRegression(max_iter=1000),
        {'C': [0.1, 1, 10], 'solver': ['liblinear', 'lbfgs']}
    ),
    "GradientBoosting": (
        GradientBoostingClassifier(),
        {'n_estimators': [50, 100], 'learning_rate': [0.05, 0.1], 'max_depth': [3, 5]}
    ),
    "AdaBoost": (
        AdaBoostClassifier(),
        {'n_estimators': [50, 100], 'learning_rate': [0.05, 0.1, 0.5]}
    ),
    "ExtraTrees": (
        ExtraTreesClassifier(),
        {'n_estimators': [50, 100], 'max_depth': [5, 10, None]}
    ),
    "NaiveBayes": (
        GaussianNB(),
        {}  # Không có tham số để tinh chỉnh
    )
}

# Huấn luyện mô hình và tìm ra mô hình tốt nhất
best_models = {}
print("\n--- Bắt đầu quá trình huấn luyện và tìm kiếm mô hình tốt nhất ---")
for name, (model, params) in models.items():
    grid = GridSearchCV(model, params, cv=kf, scoring='accuracy', n_jobs=-1) # Lựa chọn tham số tốt nhất, mô hình tốt nhất bằng GridSeachCV
    grid.fit(X_train_scaled, y_train_res) # HUẤN LUYỆN TRÊN TẬP TRAIN ĐÃ XỬ LÝ
    best_models[name] = (grid.best_score_, grid.best_estimator_)

# Tạo, sắp xếp và hiển thị bảng kết quả
metrics_df = pd.DataFrame({
    name: {"Mean CV Accuracy": score}
    for name, (score, model) in best_models.items()
}).T
metrics_df = metrics_df.sort_values(by='Mean CV Accuracy', ascending=False)
print("\nBảng so sánh hiệu quả các mô hình:")
display(metrics_df)

# Lấy ra mô hình có accuracy cao nhất trên tập cross-validation
best_model_name, (best_score, final_model) = max(best_models.items(), key=lambda item: item[1][0])
print(f"\n Mô hình tốt nhất: {best_model_name} with CV accuracy: {best_score:.4f}")

# Đánh giá hiệu quả của mô hình đã chọn trên dữ liệu chưa từng thấy
y_pred = final_model.predict(X_test_scaled)
print("\n--- ĐÁNH GIÁ HIỆU SUẤT TRÊN TẬP TEST ---")
print("\n Classification Report (trên tập test):")
print(classification_report(y_test, y_pred))

print(" Confusion Matrix (trên tập test):")
# Tính toán ma trận nhầm lẫn
cm = confusion_matrix(y_test, y_pred)

# Vẽ heatmap
plt.figure(figsize=(8, 6))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
            xticklabels=['Không thôi việc', 'Thôi việc'],
            yticklabels=['Không thôi việc', 'Thôi việc'])
plt.title('Ma trận nhầm lẫn trên tập Test')
plt.ylabel('Nhãn thực tế')
plt.xlabel('Nhãn dự đoán')
plt.show()

#Lưu mô hình và Test bằng dữ liệu (Chỉnh sửa)
# Lưu mô hình, scaler và danh sách cột
joblib.dump(final_model, 'best_model.pkl')
joblib.dump(scaler, 'scaler.pkl')
joblib.dump(processed_columns, 'processed_columns.pkl')
print("\n Mô hình, scaler và danh sách cột đã được lưu.")

def predict_attrition(sample_dict):
    """
    Hàm dự đoán khả năng nghỉ việc cho một mẫu dữ liệu mới.
    - sample_dict: một dictionary chứa dữ liệu của nhân viên.
    """
    # Tải lại các đối tượng đã lưu
    try:
        model = joblib.load('best_model.pkl')
        scaler = joblib.load('scaler.pkl')
        columns = joblib.load('processed_columns.pkl')
    except FileNotFoundError:
        print("Lỗi: Không tìm thấy file model/scaler/columns. Vui lòng huấn luyện lại mô hình.")
        return

    # Chuyển dictionary thành DataFrame
    new_sample_df = pd.DataFrame([sample_dict])

    # Áp dụng One-Hot Encoding
    new_sample_df = pd.get_dummies(new_sample_df)

    # Căn chỉnh các cột để khớp với dữ liệu lúc train
    # Cột nào có trong `columns` mà không có trong `new_sample_df` sẽ được thêm vào với giá trị 0
    # Cột nào có trong `new_sample_df` mà không có trong `columns` sẽ bị loại bỏ
    new_sample_aligned = new_sample_df.reindex(columns=columns, fill_value=0)

    # Scale mẫu mới
    new_sample_scaled = scaler.transform(new_sample_aligned)

    # Dự đoán
    prediction = model.predict(new_sample_scaled)
    probability = model.predict_proba(new_sample_scaled)

    # In kết quả
    result = 'Thôi việc' if prediction[0] == 1 else 'Không thôi việc'
    print(f"\n DỰ ĐOÁN CHO MẪU MỚI:")
    print(f"-> Kết quả: {result}")
    print(f"-> Xác suất Không thôi việc: {probability[0][0]:.2f}")
    print(f"-> Xác suất Thôi việc: {probability[0][1]:.2f}")


# Ví dụ sử dụng hàm dự đoán
# Cung cấp dữ liệu dưới dạng chưa bị mã hóa
new_employee_data = {
    'Tuổi': 35,
    'Đi_Công_Tác': 'Travel_Rarely', # Dùng tên gốc, không cần mã hóa
    'Lương_Ngày': 900,
    'Phòng_Ban': 'Sales',
    'Khoảng_Cách_Tới_Nhà': 10,
    'Trình_Độ_Học_Vấn': 3,
    'Ngành_Học': 'Marketing',
    'Mức_Độ_Hài_Lòng_Môi_Trường': 3,
    'Giới_Tính': 'Male',
    'Lương_Giờ': 60,
    'Mức_Độ_Tham_Gia_Công_Việc': 3,
    'Cấp_Bậc_Công_Việc': 2,
    'Chức_Vụ': 'Sales Executive',
    'Mức_Độ_Hài_Lòng_Công_Việc': 4,
    'Tình_Trạng_Hôn_Nhân': 'Married',
    'Lương_Tháng': 5000,
    'Lương_Tháng_Theo_Tỷ_Lệ': 15000,
    'Số_Công_Ty_Đã_Làm': 2,
    'Làm_Thêm': 'No',
    'Phần_Trăm_Tăng_Lương': 15,
    'Đánh_Giá_Hiệu_Suất': 3,
    'Mức_Hài_Lòng_Mối_Quan_Hệ': 3,
    'Cấp_Độ_Tùy_Chọn_Cổ_Phần': 0,
    'Tổng_Số_Năm_Làm_Việc': 10,
    'Số_Lần_Đào_Tạo_Năm_Trước': 2,
    'Cân_Bằng_Cuộc_Sống_Công_Việc': 3,
    'Số_Năm_Làm_Tại_Công_Ty': 5,
    'Số_Năm_Làm_Vị_Trí_Hiện_Tại': 3,
    'Số_Năm_Từ_Lần_Thăng_Chức_Gần_Nhất': 1,
    'Số_Năm_Với_Quản_Lý_Hiện_Tại': 2
}
predict_attrition(new_employee_data)

# --- KIỂM TRA VỚI DÒNG ĐẦU TIÊN CỦA BỘ DỮ LIỆU ---
print("\n" + "="*40)
print(">>> KIỂM TRA VỚI DÒNG ĐẦU TIÊN <<<")

# Chuyển Series thành dictionary
first_row_dict = first_row_to_test.to_dict()

# Lấy ra kết quả thực tế và xóa nó khỏi dictionary để mô hình không "biết trước"
actual_result_text = first_row_dict.pop('Nghỉ_Việc')
print(f"\nGiá trị thực tế: '{actual_result_text}' ({'Thôi việc' if actual_result_text == 'Yes' or actual_result_text == 1 else 'Không thôi việc'})")

# Gọi hàm dự đoán với dữ liệu dòng đầu tiên
predict_attrition(first_row_dict)
print("="*40)