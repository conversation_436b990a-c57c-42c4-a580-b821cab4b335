import warnings
import joblib
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import StratifiedKFold, train_test_split, RandomizedSearchCV
from sklearn.ensemble import (
    AdaBoostClassifier, ExtraTreesClassifier, GradientBoostingClassifier, 
    RandomForestClassifier, VotingClassifier
)
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import (
    classification_report, confusion_matrix, roc_auc_score, 
    f1_score, precision_score, recall_score, accuracy_score
)
from sklearn.naive_bayes import GaussianNB
from sklearn.neighbors import KNeighborsClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import LabelEncoder, StandardScaler, RobustScaler
from sklearn.svm import SVC
from sklearn.tree import DecisionTreeClassifier
from sklearn.feature_selection import SelectKBest, f_classif, RFE
from imblearn.over_sampling import SMOTE, ADASYN
from imblearn.under_sampling import RandomUnderSampler
from imblearn.combine import SMOTETomek
import os

# Cài đặt để tránh warning
warnings.filterwarnings("ignore")
try:
    plt.style.use('seaborn-v0_8')
except:
    plt.style.use('seaborn')

class OptimizedMLPipeline:
    def __init__(self):
        self.scaler = None
        self.best_model = None
        self.feature_selector = None
        self.processed_columns = None
        self.label_encoder = None
        self.results = {}
        
    def load_data(self):
        """Tải dữ liệu từ file CSV hoặc tạo dữ liệu mẫu"""
        print("🔍 Đang tìm kiếm file dữ liệu...")
        
        # Tìm file CSV trong thư mục hiện tại
        csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]
        
        if csv_files:
            csv_file = csv_files[0]
            print(f"✅ Đã tìm thấy file CSV: {csv_file}")
            df = pd.read_csv(csv_file)
        else:
            print("⚠️ Không tìm thấy file CSV. Tạo dữ liệu mẫu để demo...")
            df = self._create_sample_data()
            
        return df
    
    def _create_sample_data(self):
        """Tạo dữ liệu mẫu cho demo"""
        np.random.seed(42)
        n_samples = 1500
        
        df = pd.DataFrame({
            'Age': np.random.randint(18, 65, n_samples),
            'Attrition': np.random.choice(['Yes', 'No'], n_samples, p=[0.16, 0.84]),
            'BusinessTravel': np.random.choice(['Travel_Rarely', 'Travel_Frequently', 'Non-Travel'], n_samples),
            'DailyRate': np.random.randint(100, 2000, n_samples),
            'Department': np.random.choice(['Sales', 'Research & Development', 'Human Resources'], n_samples),
            'DistanceFromHome': np.random.randint(1, 30, n_samples),
            'Education': np.random.randint(1, 5, n_samples),
            'EducationField': np.random.choice(['Life Sciences', 'Medical', 'Marketing', 'Technical Degree', 'Other'], n_samples),
            'EmployeeCount': [1] * n_samples,
            'EmployeeNumber': range(1, n_samples + 1),
            'EnvironmentSatisfaction': np.random.randint(1, 5, n_samples),
            'Gender': np.random.choice(['Male', 'Female'], n_samples),
            'HourlyRate': np.random.randint(30, 100, n_samples),
            'JobInvolvement': np.random.randint(1, 5, n_samples),
            'JobLevel': np.random.randint(1, 6, n_samples),
            'JobRole': np.random.choice(['Sales Executive', 'Research Scientist', 'Laboratory Technician'], n_samples),
            'JobSatisfaction': np.random.randint(1, 5, n_samples),
            'MaritalStatus': np.random.choice(['Single', 'Married', 'Divorced'], n_samples),
            'MonthlyIncome': np.random.randint(1000, 20000, n_samples),
            'MonthlyRate': np.random.randint(2000, 30000, n_samples),
            'NumCompaniesWorked': np.random.randint(0, 10, n_samples),
            'Over18': ['Y'] * n_samples,
            'OverTime': np.random.choice(['Yes', 'No'], n_samples),
            'PercentSalaryHike': np.random.randint(10, 25, n_samples),
            'PerformanceRating': np.random.randint(3, 5, n_samples),
            'RelationshipSatisfaction': np.random.randint(1, 5, n_samples),
            'StandardHours': [80] * n_samples,
            'StockOptionLevel': np.random.randint(0, 4, n_samples),
            'TotalWorkingYears': np.random.randint(0, 40, n_samples),
            'TrainingTimesLastYear': np.random.randint(0, 7, n_samples),
            'WorkLifeBalance': np.random.randint(1, 5, n_samples),
            'YearsAtCompany': np.random.randint(0, 40, n_samples),
            'YearsInCurrentRole': np.random.randint(0, 20, n_samples),
            'YearsSinceLastPromotion': np.random.randint(0, 15, n_samples),
            'YearsWithCurrManager': np.random.randint(0, 20, n_samples)
        })
        
        return df
    
    def preprocess_data(self, df):
        """Tiền xử lý dữ liệu nâng cao"""
        print("🔧 Bắt đầu tiền xử lý dữ liệu...")
        
        # Lưu dòng đầu tiên để test
        first_row = df.iloc[0].copy()
        
        # Đổi tên cột sang tiếng Việt
        column_mapping = {
            'Age': 'Tuổi', 'Attrition': 'Nghỉ_Việc', 'BusinessTravel': 'Đi_Công_Tác',
            'DailyRate': 'Lương_Ngày', 'Department': 'Phòng_Ban', 'DistanceFromHome': 'Khoảng_Cách_Tới_Nhà',
            'Education': 'Trình_Độ_Học_Vấn', 'EducationField': 'Ngành_Học', 'EmployeeCount': 'Tổng_Nhân_Viên',
            'EmployeeNumber': 'Mã_Nhân_Viên', 'EnvironmentSatisfaction': 'Mức_Độ_Hài_Lòng_Môi_Trường',
            'Gender': 'Giới_Tính', 'HourlyRate': 'Lương_Giờ', 'JobInvolvement': 'Mức_Độ_Tham_Gia_Công_Việc',
            'JobLevel': 'Cấp_Bậc_Công_Việc', 'JobRole': 'Chức_Vụ', 'JobSatisfaction': 'Mức_Độ_Hài_Lòng_Công_Việc',
            'MaritalStatus': 'Tình_Trạng_Hôn_Nhân', 'MonthlyIncome': 'Lương_Tháng', 'MonthlyRate': 'Lương_Tháng_Theo_Tỷ_Lệ',
            'NumCompaniesWorked': 'Số_Công_Ty_Đã_Làm', 'Over18': 'Trên_18_Tuổi', 'OverTime': 'Làm_Thêm',
            'PercentSalaryHike': 'Phần_Trăm_Tăng_Lương', 'PerformanceRating': 'Đánh_Giá_Hiệu_Suất',
            'RelationshipSatisfaction': 'Mức_Hài_Lòng_Mối_Quan_Hệ', 'StandardHours': 'Giờ_Làm_Tiêu_Chuẩn',
            'StockOptionLevel': 'Cấp_Độ_Tùy_Chọn_Cổ_Phần', 'TotalWorkingYears': 'Tổng_Số_Năm_Làm_Việc',
            'TrainingTimesLastYear': 'Số_Lần_Đào_Tạo_Năm_Trước', 'WorkLifeBalance': 'Cân_Bằng_Cuộc_Sống_Công_Việc',
            'YearsAtCompany': 'Số_Năm_Làm_Tại_Công_Ty', 'YearsInCurrentRole': 'Số_Năm_Làm_Vị_Trí_Hiện_Tại',
            'YearsSinceLastPromotion': 'Số_Năm_Từ_Lần_Thăng_Chức_Gần_Nhất', 'YearsWithCurrManager': 'Số_Năm_Với_Quản_Lý_Hiện_Tại'
        }
        df.rename(columns=column_mapping, inplace=True)
        
        print(f"📊 Kích thước dữ liệu ban đầu: {df.shape}")
        
        # Loại bỏ các cột không cần thiết
        columns_to_drop = ['Mã_Nhân_Viên', 'Trên_18_Tuổi', 'Giờ_Làm_Tiêu_Chuẩn', 'Tổng_Nhân_Viên']
        df.drop([col for col in columns_to_drop if col in df.columns], axis=1, inplace=True)
        
        # Mã hóa biến mục tiêu
        self.label_encoder = LabelEncoder()
        df['Nghỉ_Việc'] = self.label_encoder.fit_transform(df['Nghỉ_Việc'])
        
        # Feature Engineering
        df = self._feature_engineering(df)
        
        # Xử lý outliers
        df = self._handle_outliers(df)
        
        # One-Hot Encoding
        categorical_cols = df.select_dtypes(include=['object']).columns
        df = pd.get_dummies(df, columns=categorical_cols, drop_first=True)
        
        print(f"📊 Kích thước sau xử lý: {df.shape}")
        
        return df, first_row

    def _feature_engineering(self, df):
        """Tạo các features mới"""
        print("🔨 Tạo features mới...")

        # Tạo features tương tác
        if 'Tuổi' in df.columns and 'Tổng_Số_Năm_Làm_Việc' in df.columns:
            df['Tuổi_Bắt_Đầu_Làm_Việc'] = df['Tuổi'] - df['Tổng_Số_Năm_Làm_Việc']

        if 'Lương_Tháng' in df.columns and 'Tuổi' in df.columns:
            df['Lương_Theo_Tuổi'] = df['Lương_Tháng'] / df['Tuổi']

        if 'Số_Năm_Làm_Tại_Công_Ty' in df.columns and 'Tổng_Số_Năm_Làm_Việc' in df.columns:
            df['Tỷ_Lệ_Năm_Tại_Công_Ty'] = df['Số_Năm_Làm_Tại_Công_Ty'] / (df['Tổng_Số_Năm_Làm_Việc'] + 1)

        if 'Số_Năm_Từ_Lần_Thăng_Chức_Gần_Nhất' in df.columns and 'Số_Năm_Làm_Tại_Công_Ty' in df.columns:
            df['Tỷ_Lệ_Thăng_Chức'] = df['Số_Năm_Từ_Lần_Thăng_Chức_Gần_Nhất'] / (df['Số_Năm_Làm_Tại_Công_Ty'] + 1)

        # Tạo features nhóm
        satisfaction_cols = [col for col in df.columns if 'Hài_Lòng' in col or 'Satisfaction' in col]
        if satisfaction_cols:
            df['Mức_Hài_Lòng_Trung_Bình'] = df[satisfaction_cols].mean(axis=1)

        return df

    def _handle_outliers(self, df):
        """Xử lý outliers bằng IQR method"""
        print("🧹 Xử lý outliers...")

        numeric_cols = df.select_dtypes(include=[np.number]).columns
        numeric_cols = [col for col in numeric_cols if col != 'Nghỉ_Việc']

        for col in numeric_cols:
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR

            # Thay thế outliers bằng giá trị boundary
            df[col] = np.where(df[col] < lower_bound, lower_bound, df[col])
            df[col] = np.where(df[col] > upper_bound, upper_bound, df[col])

        return df

    def prepare_data(self, df):
        """Chuẩn bị dữ liệu cho training"""
        print("📋 Chuẩn bị dữ liệu cho training...")

        # Tách features và target
        X = df.drop('Nghỉ_Việc', axis=1)
        y = df['Nghỉ_Việc']

        # Chia train/test
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )

        print(f"📊 Phân phối lớp gốc: {y.value_counts().to_dict()}")

        # Feature Selection
        self.feature_selector = SelectKBest(score_func=f_classif, k=min(50, X_train.shape[1]))
        X_train_selected = self.feature_selector.fit_transform(X_train, y_train)
        X_test_selected = self.feature_selector.transform(X_test)

        # Lưu tên cột đã chọn
        selected_features = X.columns[self.feature_selector.get_support()]
        self.processed_columns = selected_features.tolist()

        print(f"🎯 Đã chọn {len(self.processed_columns)} features quan trọng nhất")

        # Sampling để cân bằng dữ liệu
        smote_tomek = SMOTETomek(random_state=42)
        X_train_balanced, y_train_balanced = smote_tomek.fit_resample(X_train_selected, y_train)

        print(f"⚖️ Phân phối sau cân bằng: {pd.Series(y_train_balanced).value_counts().to_dict()}")

        # Scaling
        self.scaler = RobustScaler()
        X_train_scaled = self.scaler.fit_transform(X_train_balanced)
        X_test_scaled = self.scaler.transform(X_test_selected)

        return X_train_scaled, X_test_scaled, y_train_balanced, y_test

    def get_advanced_models(self):
        """Định nghĩa các mô hình và hyperparameters nâng cao"""
        models = {
            "RandomForest": {
                "model": RandomForestClassifier(random_state=42, n_jobs=-1),
                "params": {
                    'n_estimators': [100, 200, 300],
                    'max_depth': [10, 20, None],
                    'min_samples_split': [2, 5, 10],
                    'min_samples_leaf': [1, 2, 4],
                    'max_features': ['sqrt', 'log2']
                }
            },
            "GradientBoosting": {
                "model": GradientBoostingClassifier(random_state=42),
                "params": {
                    'n_estimators': [100, 200],
                    'learning_rate': [0.05, 0.1, 0.15],
                    'max_depth': [3, 5, 7],
                    'subsample': [0.8, 0.9, 1.0]
                }
            },
            "SVM": {
                "model": SVC(probability=True, random_state=42),
                "params": {
                    'C': [0.1, 1, 10, 100],
                    'kernel': ['rbf', 'poly'],
                    'gamma': ['scale', 'auto']
                }
            },
            "LogisticRegression": {
                "model": LogisticRegression(random_state=42, max_iter=1000),
                "params": {
                    'C': [0.01, 0.1, 1, 10, 100],
                    'solver': ['liblinear', 'lbfgs'],
                    'penalty': ['l1', 'l2']
                }
            },
            "ExtraTrees": {
                "model": ExtraTreesClassifier(random_state=42, n_jobs=-1),
                "params": {
                    'n_estimators': [100, 200],
                    'max_depth': [10, 20, None],
                    'min_samples_split': [2, 5],
                    'min_samples_leaf': [1, 2]
                }
            },
            "MLP": {
                "model": MLPClassifier(random_state=42, max_iter=500),
                "params": {
                    'hidden_layer_sizes': [(50,), (100,), (50, 50), (100, 50)],
                    'activation': ['relu', 'tanh'],
                    'alpha': [0.0001, 0.001, 0.01],
                    'learning_rate': ['constant', 'adaptive']
                }
            }
        }

        return models

    def train_models(self, X_train, y_train):
        """Training các mô hình với RandomizedSearchCV"""
        print("🚀 Bắt đầu training các mô hình...")

        models = self.get_advanced_models()
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)

        best_models = {}

        for name, config in models.items():
            print(f"🔄 Đang training {name}...")

            # Sử dụng RandomizedSearchCV thay vì GridSearchCV để tăng tốc
            search = RandomizedSearchCV(
                config["model"],
                config["params"],
                cv=cv,
                scoring='f1',  # Sử dụng F1 score thay vì accuracy
                n_jobs=-1,
                n_iter=20,  # Giảm số lần thử để tăng tốc
                random_state=42,
                verbose=0
            )

            search.fit(X_train, y_train)
            best_models[name] = {
                'model': search.best_estimator_,
                'score': search.best_score_,
                'params': search.best_params_
            }

            print(f"✅ {name} - Best F1 Score: {search.best_score_:.4f}")

        self.results = best_models
        return best_models

    def evaluate_models(self, X_test, y_test):
        """Đánh giá chi tiết các mô hình"""
        print("📊 Đánh giá hiệu suất các mô hình...")

        evaluation_results = {}

        for name, model_info in self.results.items():
            model = model_info['model']
            y_pred = model.predict(X_test)
            y_pred_proba = model.predict_proba(X_test)[:, 1]

            # Tính toán các metrics
            metrics = {
                'Accuracy': accuracy_score(y_test, y_pred),
                'Precision': precision_score(y_test, y_pred),
                'Recall': recall_score(y_test, y_pred),
                'F1_Score': f1_score(y_test, y_pred),
                'ROC_AUC': roc_auc_score(y_test, y_pred_proba),
                'CV_F1_Score': model_info['score']
            }

            evaluation_results[name] = metrics

        # Tạo DataFrame để hiển thị kết quả
        results_df = pd.DataFrame(evaluation_results).T
        results_df = results_df.sort_values('F1_Score', ascending=False)

        print("\n🏆 BẢNG XẾP HẠNG CÁC MÔ HÌNH:")
        print("="*80)
        print(results_df.round(4))

        # Chọn mô hình tốt nhất
        best_model_name = results_df.index[0]
        self.best_model = self.results[best_model_name]['model']

        print(f"\n🥇 MÔ HÌNH TỐT NHẤT: {best_model_name}")
        print(f"📈 F1 Score: {results_df.loc[best_model_name, 'F1_Score']:.4f}")
        print(f"📈 ROC-AUC: {results_df.loc[best_model_name, 'ROC_AUC']:.4f}")

        return results_df, best_model_name

    def create_ensemble(self, X_train, y_train):
        """Tạo ensemble model từ top 3 mô hình"""
        print("🎭 Tạo Ensemble Model...")

        # Lấy top 3 mô hình
        sorted_models = sorted(self.results.items(), key=lambda x: x[1]['score'], reverse=True)[:3]

        estimators = [(name, info['model']) for name, info in sorted_models]

        # Tạo Voting Classifier
        ensemble = VotingClassifier(estimators=estimators, voting='soft')
        ensemble.fit(X_train, y_train)

        print(f"✅ Ensemble tạo từ: {[name for name, _ in estimators]}")

        return ensemble

    def save_model(self, model_name="best_model"):
        """Lưu mô hình và các components"""
        print("💾 Lưu mô hình...")

        # Lưu các components
        joblib.dump(self.best_model, f'{model_name}.pkl')
        joblib.dump(self.scaler, f'{model_name}_scaler.pkl')
        joblib.dump(self.feature_selector, f'{model_name}_feature_selector.pkl')
        joblib.dump(self.processed_columns, f'{model_name}_columns.pkl')
        joblib.dump(self.label_encoder, f'{model_name}_label_encoder.pkl')

        print(f"✅ Đã lưu mô hình và components với tên: {model_name}")

    def predict_new_sample(self, sample_dict, model_name="best_model"):
        """Dự đoán cho mẫu mới"""
        try:
            # Load các components
            model = joblib.load(f'{model_name}.pkl')
            scaler = joblib.load(f'{model_name}_scaler.pkl')
            feature_selector = joblib.load(f'{model_name}_feature_selector.pkl')
            columns = joblib.load(f'{model_name}_columns.pkl')
            label_encoder = joblib.load(f'{model_name}_label_encoder.pkl')

            # Xử lý sample mới
            sample_df = pd.DataFrame([sample_dict])

            # Feature engineering (cần implement tương tự như trong training)
            sample_df = self._feature_engineering(sample_df)

            # One-hot encoding
            sample_df = pd.get_dummies(sample_df)

            # Align columns
            sample_df = sample_df.reindex(columns=columns, fill_value=0)

            # Feature selection
            sample_selected = feature_selector.transform(sample_df)

            # Scaling
            sample_scaled = scaler.transform(sample_selected)

            # Prediction
            prediction = model.predict(sample_scaled)[0]
            probability = model.predict_proba(sample_scaled)[0]

            # Decode prediction
            result = label_encoder.inverse_transform([prediction])[0]

            print(f"\n🔮 DỰ ĐOÁN CHO MẪU MỚI:")
            print(f"➤ Kết quả: {result}")
            print(f"➤ Xác suất Không nghỉ việc: {probability[0]:.3f}")
            print(f"➤ Xác suất Nghỉ việc: {probability[1]:.3f}")

            return result, probability

        except FileNotFoundError:
            print("❌ Không tìm thấy file mô hình. Vui lòng train lại mô hình.")
            return None, None

def main():
    """Hàm chính để chạy toàn bộ pipeline"""
    print("🎯 KHỞI ĐỘNG PIPELINE TỐI ỨU HÓA MÔ HÌNH MACHINE LEARNING")
    print("="*70)

    # Khởi tạo pipeline
    pipeline = OptimizedMLPipeline()

    # 1. Load và preprocess data
    df = pipeline.load_data()
    df_processed, first_row = pipeline.preprocess_data(df)

    # 2. Prepare data
    X_train, X_test, y_train, y_test = pipeline.prepare_data(df_processed)

    # 3. Train models
    best_models = pipeline.train_models(X_train, y_train)

    # 4. Evaluate models
    results_df, best_model_name = pipeline.evaluate_models(X_test, y_test)

    # 5. Create ensemble
    ensemble_model = pipeline.create_ensemble(X_train, y_train)

    # Evaluate ensemble
    y_pred_ensemble = ensemble_model.predict(X_test)
    y_pred_proba_ensemble = ensemble_model.predict_proba(X_test)[:, 1]

    ensemble_metrics = {
        'Accuracy': accuracy_score(y_test, y_pred_ensemble),
        'Precision': precision_score(y_test, y_pred_ensemble),
        'Recall': recall_score(y_test, y_pred_ensemble),
        'F1_Score': f1_score(y_test, y_pred_ensemble),
        'ROC_AUC': roc_auc_score(y_test, y_pred_proba_ensemble)
    }

    print(f"\n🎭 ENSEMBLE MODEL PERFORMANCE:")
    for metric, value in ensemble_metrics.items():
        print(f"➤ {metric}: {value:.4f}")

    # So sánh với mô hình tốt nhất
    best_f1 = results_df.loc[best_model_name, 'F1_Score']
    ensemble_f1 = ensemble_metrics['F1_Score']

    if ensemble_f1 > best_f1:
        print(f"\n🏆 ENSEMBLE MODEL THẮNG! ({ensemble_f1:.4f} vs {best_f1:.4f})")
        final_model = ensemble_model
        model_type = "ensemble"
    else:
        print(f"\n🏆 {best_model_name.upper()} THẮNG! ({best_f1:.4f} vs {ensemble_f1:.4f})")
        final_model = pipeline.best_model
        model_type = "single"

    # 6. Save final model
    if model_type == "ensemble":
        joblib.dump(final_model, 'final_ensemble_model.pkl')
        joblib.dump(pipeline.scaler, 'final_ensemble_scaler.pkl')
        joblib.dump(pipeline.feature_selector, 'final_ensemble_feature_selector.pkl')
        joblib.dump(pipeline.processed_columns, 'final_ensemble_columns.pkl')
        joblib.dump(pipeline.label_encoder, 'final_ensemble_label_encoder.pkl')
        print("💾 Đã lưu Ensemble Model")
    else:
        pipeline.save_model("final_best_model")

    # 7. Test với dữ liệu mẫu
    print("\n" + "="*50)
    print("🧪 KIỂM TRA VỚI DỮ LIỆU MẪU")

    # Tạo dữ liệu test mẫu
    sample_data = {
        'Age': 35, 'BusinessTravel': 'Travel_Rarely', 'DailyRate': 900,
        'Department': 'Sales', 'DistanceFromHome': 10, 'Education': 3,
        'EducationField': 'Marketing', 'EnvironmentSatisfaction': 3,
        'Gender': 'Male', 'HourlyRate': 60, 'JobInvolvement': 3,
        'JobLevel': 2, 'JobRole': 'Sales Executive', 'JobSatisfaction': 4,
        'MaritalStatus': 'Married', 'MonthlyIncome': 5000, 'MonthlyRate': 15000,
        'NumCompaniesWorked': 2, 'OverTime': 'No', 'PercentSalaryHike': 15,
        'PerformanceRating': 3, 'RelationshipSatisfaction': 3,
        'StockOptionLevel': 0, 'TotalWorkingYears': 10,
        'TrainingTimesLastYear': 2, 'WorkLifeBalance': 3,
        'YearsAtCompany': 5, 'YearsInCurrentRole': 3,
        'YearsSinceLastPromotion': 1, 'YearsWithCurrManager': 2
    }

    if model_type == "single":
        pipeline.predict_new_sample(sample_data, "final_best_model")

    print("\n🎉 HOÀN THÀNH PIPELINE TỐI ỨU HÓA!")
    print("="*70)

    return pipeline, final_model, results_df

if __name__ == "__main__":
    pipeline, final_model, results = main()
