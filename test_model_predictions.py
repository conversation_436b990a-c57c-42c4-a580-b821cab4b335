#!/usr/bin/env python3
"""
Test script to validate model predictions
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from optimized_ml_model import OptimizedMLPipeline

def test_model_predictions():
    """Test the saved model with various sample data"""
    print("🧪 TESTING MODEL PREDICTIONS")
    print("="*50)
    
    # Initialize pipeline
    pipeline = OptimizedMLPipeline()
    
    # Test cases with different employee profiles
    test_cases = [
        {
            "name": "High Risk Employee",
            "data": {
                'Age': 25, 'BusinessTravel': 'Travel_Frequently', 'DailyRate': 500,
                'Department': 'Sales', 'DistanceFromHome': 25, 'Education': 1,
                'EducationField': 'Other', 'EnvironmentSatisfaction': 1,
                'Gender': 'Male', 'HourlyRate': 30, 'JobInvolvement': 1,
                'JobLevel': 1, 'JobRole': 'Sales Executive', 'JobSatisfaction': 1,
                'MaritalStatus': 'Single', 'MonthlyIncome': 2000, 'MonthlyRate': 5000,
                'NumCompaniesWorked': 5, 'OverTime': 'Yes', 'PercentSalaryHike': 10,
                'PerformanceRating': 3, 'RelationshipSatisfaction': 1,
                'StockOptionLevel': 0, 'TotalWorkingYears': 2,
                'TrainingTimesLastYear': 0, 'WorkLifeBalance': 1,
                'YearsAtCompany': 1, 'YearsInCurrentRole': 0,
                'YearsSinceLastPromotion': 5, 'YearsWithCurrManager': 0
            }
        },
        {
            "name": "Low Risk Employee", 
            "data": {
                'Age': 45, 'BusinessTravel': 'Travel_Rarely', 'DailyRate': 1500,
                'Department': 'Research & Development', 'DistanceFromHome': 5, 'Education': 4,
                'EducationField': 'Life Sciences', 'EnvironmentSatisfaction': 4,
                'Gender': 'Female', 'HourlyRate': 80, 'JobInvolvement': 4,
                'JobLevel': 4, 'JobRole': 'Research Scientist', 'JobSatisfaction': 4,
                'MaritalStatus': 'Married', 'MonthlyIncome': 15000, 'MonthlyRate': 25000,
                'NumCompaniesWorked': 1, 'OverTime': 'No', 'PercentSalaryHike': 20,
                'PerformanceRating': 4, 'RelationshipSatisfaction': 4,
                'StockOptionLevel': 3, 'TotalWorkingYears': 20,
                'TrainingTimesLastYear': 5, 'WorkLifeBalance': 4,
                'YearsAtCompany': 15, 'YearsInCurrentRole': 10,
                'YearsSinceLastPromotion': 2, 'YearsWithCurrManager': 8
            }
        },
        {
            "name": "Medium Risk Employee",
            "data": {
                'Age': 35, 'BusinessTravel': 'Travel_Rarely', 'DailyRate': 900,
                'Department': 'Sales', 'DistanceFromHome': 10, 'Education': 3,
                'EducationField': 'Marketing', 'EnvironmentSatisfaction': 3,
                'Gender': 'Male', 'HourlyRate': 60, 'JobInvolvement': 3,
                'JobLevel': 2, 'JobRole': 'Sales Executive', 'JobSatisfaction': 4,
                'MaritalStatus': 'Married', 'MonthlyIncome': 5000, 'MonthlyRate': 15000,
                'NumCompaniesWorked': 2, 'OverTime': 'No', 'PercentSalaryHike': 15,
                'PerformanceRating': 3, 'RelationshipSatisfaction': 3,
                'StockOptionLevel': 0, 'TotalWorkingYears': 10,
                'TrainingTimesLastYear': 2, 'WorkLifeBalance': 3,
                'YearsAtCompany': 5, 'YearsInCurrentRole': 3,
                'YearsSinceLastPromotion': 1, 'YearsWithCurrManager': 2
            }
        }
    ]
    
    # Test each case
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test Case {i}: {test_case['name']}")
        print("-" * 40)
        
        try:
            result, probability = pipeline.predict_new_sample(
                test_case['data'], 
                "final_best_model"
            )
            
            if result is not None:
                print(f"✅ Prediction successful!")
                print(f"   Result: {result}")
                print(f"   Confidence: {max(probability):.3f}")
            else:
                print(f"❌ Prediction failed!")
                
        except Exception as e:
            print(f"❌ Error during prediction: {e}")
    
    print(f"\n🎯 Model prediction testing complete!")

if __name__ == "__main__":
    test_model_predictions()
